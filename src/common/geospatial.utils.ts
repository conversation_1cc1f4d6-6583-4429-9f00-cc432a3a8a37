import { FilterQuery } from 'mongoose';

/**
 * Utility functions for geospatial operations using location.center field
 */

/**
 * Create a MongoDB query for finding documents near a specific location
 * @param longitude - Longitude coordinate
 * @param latitude - Latitude coordinate
 * @param maxDistanceMeters - Maximum distance in meters (default: 10000m = 10km)
 * @returns MongoDB filter query for $near operation
 */
export function createNearQuery(
  longitude: number,
  latitude: number,
  maxDistanceMeters: number = 10000,
): FilterQuery<any> {
  return {
    'location.center': {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude],
        },
        $maxDistance: maxDistanceMeters,
      },
    },
  };
}

/**
 * Create a MongoDB query for finding documents within a specific radius
 * @param longitude - Longitude coordinate
 * @param latitude - Latitude coordinate
 * @param radiusMeters - Radius in meters
 * @returns MongoDB filter query for $geoWithin operation
 */
export function createWithinRadiusQuery(
  longitude: number,
  latitude: number,
  radiusMeters: number,
): FilterQuery<any> {
  // Convert meters to radians (Earth radius ≈ 6378100 meters)
  const radiusRadians = radiusMeters / 6378100;

  return {
    'location.center': {
      $geoWithin: {
        $centerSphere: [[longitude, latitude], radiusRadians],
      },
    },
  };
}

/**
 * Create a MongoDB query for finding documents within a bounding box
 * @param southWestLng - Southwest longitude
 * @param southWestLat - Southwest latitude
 * @param northEastLng - Northeast longitude
 * @param northEastLat - Northeast latitude
 * @returns MongoDB filter query for $geoWithin operation
 */
export function createBoundingBoxQuery(
  southWestLng: number,
  southWestLat: number,
  northEastLng: number,
  northEastLat: number,
): FilterQuery<any> {
  return {
    'location.center': {
      $geoWithin: {
        $box: [
          [southWestLng, southWestLat],
          [northEastLng, northEastLat],
        ],
      },
    },
  };
}

/**
 * Calculate distance between two points in meters using Haversine formula
 * @param lat1 - Latitude of first point
 * @param lng1 - Longitude of first point
 * @param lat2 - Latitude of second point
 * @param lng2 - Longitude of second point
 * @returns Distance in meters
 */
export function calculateDistance(
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number,
): number {
  const R = 6371000; // Earth's radius in meters
  const dLat = toRadians(lat2 - lat1);
  const dLng = toRadians(lng2 - lng1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) *
      Math.cos(toRadians(lat2)) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * Convert degrees to radians
 * @param degrees - Angle in degrees
 * @returns Angle in radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Create aggregation pipeline for sorting by distance from a point
 * @param longitude - Longitude coordinate
 * @param latitude - Latitude coordinate
 * @param maxDistanceMeters - Maximum distance in meters (optional)
 * @returns MongoDB aggregation pipeline
 */
export function createDistanceSortPipeline(
  longitude: number,
  latitude: number,
  maxDistanceMeters?: number,
): any[] {
  const pipeline = [
    {
      $geoNear: {
        near: {
          type: 'Point',
          coordinates: [longitude, latitude],
        },
        distanceField: 'distance',
        spherical: true,
        key: 'location.center',
        ...(maxDistanceMeters && { maxDistance: maxDistanceMeters }),
      },
    },
  ];

  return pipeline;
}
