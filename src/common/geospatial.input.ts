import { InputType, Field, Float } from '@nestjs/graphql';
import { IsNumber, IsOptional, Min, Max } from 'class-validator';

@InputType()
export class LocationQueryInput {
  /** Longitude coordinate */
  @Field(() => Float)
  @IsNumber()
  @Min(-180)
  @Max(180)
  longitude: number;

  /** Latitude coordinate */
  @Field(() => Float)
  @IsNumber()
  @Min(-90)
  @Max(90)
  latitude: number;

  /** Maximum distance in meters (default: 10000m = 10km) */
  @Field(() => Float, { nullable: true, defaultValue: 10000 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxDistance?: number;
}

@InputType()
export class BoundingBoxInput {
  /** Southwest longitude */
  @Field(() => Float)
  @IsNumber()
  @Min(-180)
  @Max(180)
  southWestLng: number;

  /** Southwest latitude */
  @Field(() => Float)
  @IsNumber()
  @Min(-90)
  @Max(90)
  southWestLat: number;

  /** Northeast longitude */
  @Field(() => Float)
  @IsNumber()
  @Min(-180)
  @Max(180)
  northEastLng: number;

  /** Northeast latitude */
  @Field(() => Float)
  @IsNumber()
  @Min(-90)
  @Max(90)
  northEastLat: number;
}

@InputType()
export class RadiusQueryInput {
  /** Center longitude coordinate */
  @Field(() => Float)
  @IsNumber()
  @Min(-180)
  @Max(180)
  longitude: number;

  /** Center latitude coordinate */
  @Field(() => Float)
  @IsNumber()
  @Min(-90)
  @Max(90)
  latitude: number;

  /** Radius in meters */
  @Field(() => Float)
  @IsNumber()
  @Min(1)
  radiusMeters: number;
}
