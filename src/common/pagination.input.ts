import {
  InputType,
  ObjectType,
  Field,
  registerEnumType,
} from '@nestjs/graphql';
import { IsOptional, IsPositive, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { fromPairs } from 'lodash';

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

registerEnumType(SortOrder, { name: 'SortOrder' });

@InputType()
export class SortConfigInput {
  /** Field to sort by */

  field: string;

  /** Sort order: "asc" or "desc" */
  order: SortOrder = SortOrder.DESC;
}

@InputType()
export class PaginationInput {
  /** Page number (1-based) */

  @IsOptional()
  @Type(() => Number)
  @IsPositive()
  @Min(1)
  page: number = 1;

  /** Number of items per page */

  @IsOptional()
  @Type(() => Number)
  @IsPositive()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => Math.min(value as number, 100))
  limit: number = 100;

  /** Sort configuration array */
  @IsOptional()
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return fromPairs(
        value.map((config: SortConfigInput) => [config.field, config.order]),
      );
    }
    return [{ field: 'createdAt', order: SortOrder.DESC }];
  })
  sortConfig: SortConfigInput[] = [
    { field: 'createdAt', order: SortOrder.DESC },
  ];
}

// Generic paginated response type factory
export function createPaginatedType<T>(ItemType: new () => T) {
  @ObjectType({ isAbstract: true })
  abstract class PaginatedType {
    /** Array of documents */
    @Field(() => [ItemType])
    docs: T[];

    /** Total number of documents */

    totalDocs: number;

    /** Number of documents per page */

    limit: number;

    /** Current page number */

    page: number;

    /** Total number of pages */

    totalPages: number;

    /** Previous page number */

    prevPage?: number;

    /** Next page number */

    nextPage?: number;

    /** Whether there is a previous page */

    hasPrevPage: boolean;

    /** Whether there is a next page */

    hasNextPage: boolean;

    /** Starting index of documents on current page */

    pagingCounter?: number;
  }

  return PaginatedType;
}
