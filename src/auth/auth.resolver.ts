import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { CreateUserInput } from 'src/users/dto/create-user.input';
import { User } from 'src/users/entities/user.entity';
import { AuthService } from './auth.service';
import { CurrentUser } from './decorators/current-user.decorator';
import { SignInInput, ChangePasswordInput } from './dto/auth.input';
import { AuthOutput, ChangePasswordOutput } from './entites/auth.entity';
import { Public } from './guards/auth.guard';

@Resolver(() => AuthOutput)
export class AuthResolver {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @Mutation(() => AuthOutput)
  signIn(@Args('input') input: SignInInput) {
    return this.authService.signIn(input);
  }

  @Public()
  @Mutation(() => AuthOutput)
  signUp(@Args('input') input: CreateUserInput) {
    return this.authService.signUp(input);
  }

  @Query(() => User, { name: 'me', description: 'Logged in  user' })
  me(@CurrentUser() user: User) {
    return user;
  }

  @Mutation(() => ChangePasswordOutput)
  changePassword(
    @CurrentUser() user: User,
    @Args('input') input: ChangePasswordInput,
  ) {
    return this.authService.changePassword(user, input);
  }
}
