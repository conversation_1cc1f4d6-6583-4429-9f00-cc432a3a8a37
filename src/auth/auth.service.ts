import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { hash, verify } from 'argon2';
import { CreateUserInput } from 'src/users/dto/create-user.input';
import { UsersService } from 'src/users/users.service';
import { SignInInput, ChangePasswordInput } from './dto/auth.input';
import { User } from 'src/users/entities/user.entity';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  async signIn(input: SignInInput) {
    const user = await this.usersService.findOne({
      email: input.email,
    });

    if (!user) throw new NotFoundException('phone or password is incorrect');

    const isValid = await verify(user.password, input.password);
    if (!isValid) throw new NotFoundException('phone or password is incorrect');

    const tokenPayload = { userId: user._id.toString() };
    return { access_token: await this.jwtService.signAsync(tokenPayload) };
  }

  async signUp(input: CreateUserInput) {
    const hashedPassword = await hash(input.password);

    const user = await this.usersService.create({
      ...input,
      password: hashedPassword,
    });

    const tokenPayload = { userId: user._id.toString() };
    return { access_token: await this.jwtService.signAsync(tokenPayload) };
  }

  async changePassword(user: User, input: ChangePasswordInput) {
    // Verify current password
    const isCurrentPasswordValid = await verify(
      user.password,
      input.currentPassword,
    );
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    // Check if new password is different from current password
    const isSamePassword = await verify(user.password, input.newPassword);
    if (isSamePassword) {
      throw new BadRequestException(
        'New password must be different from current password',
      );
    }

    // Hash new password
    const hashedNewPassword = await hash(input.newPassword);

    // Update user password
    await this.usersService.update(user._id.toString(), {
      password: hashedNewPassword,
    });

    return { message: 'Password changed successfully' };
  }
}
