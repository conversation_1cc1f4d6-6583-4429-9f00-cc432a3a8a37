import { InputType } from '@nestjs/graphql';
import { Transform } from 'class-transformer';
import { IsEmail, IsString, MinLength } from 'class-validator';

@InputType()
export class SignInInput {
  /** User phone number */
  @IsEmail()
  @Transform(({ value }) => (value as string).toLowerCase())
  email: string;

  /** User password */
  @IsString()
  @MinLength(4)
  password: string;
}

@InputType()
export class ChangePasswordInput {
  /** Current password */
  @IsString()
  @MinLength(4)
  currentPassword: string;

  /** New password */
  @IsString()
  @MinLength(4)
  newPassword: string;
}
