import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { ClubCategoriesService } from './club-categories.service';
import {
  ClubCategory,
  PaginatedClubCategories,
} from './entities/club-categories.entity';
import { CreateClubCategoryInput } from './dto/create-club-category.input';
import { UpdateClubCategoryInput } from './dto/update-club-category.input';
import { ClubCategoriesInput } from './dto/club-categories.input';
import { PaginationInput } from 'src/common/pagination.input';
import * as _ from 'lodash';
import { BadRequestException } from '@nestjs/common';

@Resolver(() => ClubCategory)
export class ClubCategoriesResolver {
  constructor(private readonly clubCategoriesService: ClubCategoriesService) {}

  @Mutation(() => ClubCategory, { description: 'Create a new club category' })
  createClubCategory(
    @Args('createClubCategoryInput')
    createClubCategoryInput: CreateClubCategoryInput,
  ): Promise<ClubCategory> {
    return this.clubCategoriesService.create(createClubCategoryInput);
  }

  @Query(() => PaginatedClubCategories, {
    name: 'clubCategories',
    description: 'Get all club categories with pagination and filtering',
  })
  findAllClubCategories(
    @Args('clubCategoriesInput', { nullable: true })
    clubCategoriesInput?: ClubCategoriesInput,
    @Args('paginationInput', { type: () => PaginationInput, nullable: true })
    paginationInput?: PaginationInput,
  ) {
    const filter = _.omitBy(
      _.omit(clubCategoriesInput, 'pagination'),
      (value) => _.isUndefined(value),
    );

    // Add regex search for name if provided
    if (filter.name) {
      (filter.name as any) = { $regex: new RegExp(filter.name, 'i') };
    }

    return this.clubCategoriesService.findAll(
      filter,
      paginationInput || clubCategoriesInput?.pagination,
    );
  }

  @Query(() => ClubCategory, {
    name: 'clubCategory',
    description: 'Get a single club category by ID or name',
  })
  findClubCategoryById(
    @Args('id', { type: () => ID, nullable: true }) id?: string,
    @Args('name', { type: () => String, nullable: true }) name?: string,
  ): Promise<ClubCategory> {
    if (!id && !name) {
      throw new BadRequestException('Must provide either id or name');
    }
    return this.clubCategoriesService.findOne(id ? { _id: id } : { name });
  }

  @Mutation(() => ClubCategory, {
    description: 'Update an existing club category',
  })
  updateClubCategory(
    @Args('updateClubCategoryInput')
    updateClubCategoryInput: UpdateClubCategoryInput,
  ): Promise<ClubCategory> {
    return this.clubCategoriesService.update(
      updateClubCategoryInput.id,
      updateClubCategoryInput,
    );
  }

  @Mutation(() => ClubCategory, { description: 'Delete a club category' })
  deleteClubCategory(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<ClubCategory> {
    return this.clubCategoriesService.remove(id);
  }
}
