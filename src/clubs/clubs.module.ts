import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ClubsService } from './clubs.service';
import { ClubsResolver } from './clubs.resolver';
import { ClubCategoriesService } from './club-categories.service';
import { ClubCategoriesResolver } from './club-categories.resolver';
import { Club, ClubSchema } from './entities/club.entity';
import {
  ClubCategory,
  ClubCategorySchema,
} from './entities/club-categories.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Club.name, schema: ClubSchema },
      { name: ClubCategory.name, schema: ClubCategorySchema },
    ]),
  ],
  providers: [
    ClubsResolver,
    ClubsService,
    ClubCategoriesResolver,
    ClubCategoriesService,
  ],
  exports: [ClubsService, ClubCategoriesService],
})
export class ClubsModule {}
