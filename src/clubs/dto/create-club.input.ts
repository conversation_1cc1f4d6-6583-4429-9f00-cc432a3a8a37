import { InputType, Field } from '@nestjs/graphql';
import {
  AddressInput,
  DayOfWeek,
  DayTimingInput,
  LocationInput,
} from 'src/common/common.entity';
import { ZodValidation } from 'src/common/zod-validator/decorators/zod-validation.decorator';
import { ContactInput } from 'src/users/dto/create-user.input';
import { z } from 'zod';
import { ClubStatus, MenuItemAvailability } from '../entities/club.entity';

const createClubInputSchema = z.object({
  name: z.string(),
  slug: z
    .string()
    .min(3, 'Slug must be at least 3 characters long')
    .max(100, 'Slug must not exceed 100 characters')
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      'Slug must be lowercase, contain only letters, numbers, and hyphens, and not start or end with a hyphen',
    ),
  description: z.string(),
  categories: z.array(z.string()),
  status: z.nativeEnum(ClubStatus),
  city: z.string(),
  neighborhood: z.string().optional(),
  location: z.object({
    center: z.array(z.number()).length(2),
    coords: z.array(z.array(z.number()).length(2)).optional(),
  }),
  logo: z.string().optional(),
  coverImage: z.string().optional(),
  images: z.array(z.string()),
  phone: z
    .object({
      countryCode: z.string(),
      phone: z.string(),
    })
    .optional(),
  address: z
    .object({
      address: z.string(),
      location: z.object({
        center: z.array(z.number()).length(2),
        coords: z.array(z.array(z.number()).length(2)).optional(),
      }),
      metroLine: z.string().optional(),
      metroStation: z.string().optional(),
    })
    .optional(),
  businessHours: z
    .object({
      schedule: z.array(
        z.object({
          day: z.nativeEnum(DayOfWeek),
          timings: z.array(z.number()).min(1).max(2),
        }),
      ),
    })
    .optional(),
  menu: z
    .object({
      currency: z.string(),
      sections: z.array(
        z.object({
          name: z.string(),
          items: z.array(
            z.object({
              name: z.string(),
              price: z.number().min(0),
              available: z.nativeEnum(MenuItemAvailability),
            }),
          ),
        }),
      ),
    })
    .optional(),
  featured: z.boolean(),
  rating: z.number().min(0).max(5),
});

@InputType()
export class BusinessHoursInput {
  /** Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format */
  @Field(() => [DayTimingInput])
  schedule: DayTimingInput[];
}

@InputType()
export class MenuItemInput {
  /** Menu item name */
  @Field()
  name: string;

  /** Menu item price */
  @Field()
  price: number;

  /** Whether the item is available */
  @Field(() => MenuItemAvailability)
  available: MenuItemAvailability;
}

@InputType()
export class MenuSectionInput {
  /** Section name (e.g., "Bières (pression)50cl", "Pizza", "Plats") */
  @Field()
  name: string;

  /** Array of menu items in this section */
  @Field(() => [MenuItemInput])
  items: MenuItemInput[];
}

@InputType()
export class MenuInput {
  /** Menu currency (e.g., USD, EUR, INR) */
  @Field()
  currency: string;

  /** Array of menu sections */
  @Field(() => [MenuSectionInput])
  sections: MenuSectionInput[];
}

@ZodValidation(createClubInputSchema)
@InputType()
export class CreateClubInput {
  /** Club name */
  @Field()
  name: string;

  /** SEO-friendly URL slug for the club */
  @Field()
  slug: string;

  /** Club description */
  @Field()
  description: string;

  /** Club city IDs */
  @Field()
  city: string;

  /** Optional neighborhood ID */
  @Field({ nullable: true })
  neighborhood?: string;

  /** Geographical location of the club */
  @Field(() => LocationInput)
  location: LocationInput;

  /** Club category IDs */
  @Field(() => [String])
  categories: string[];

  /** Club status */
  @Field(() => ClubStatus)
  status: ClubStatus;

  /** Club logo URL */
  @Field({ nullable: true })
  logo?: string;

  /** Club cover image URL */
  @Field({ nullable: true })
  coverImage?: string;

  /** Array of club image URLs */
  @Field(() => [String])
  images: string[];

  /** Club contact information (phone) */
  @Field(() => ContactInput, { nullable: true })
  contact?: ContactInput;

  /** Club address */
  @Field(() => AddressInput, { nullable: true })
  address?: AddressInput;

  /** Opening hours */
  @Field(() => BusinessHoursInput, { nullable: true })
  businessHours?: BusinessHoursInput;

  /** Club menu */
  @Field(() => MenuInput, { nullable: true })
  menu?: MenuInput;

  /** Whether club is featured */
  @Field()
  featured: boolean;

  /** Club rating (0-5) */
  @Field()
  rating: number;
}
