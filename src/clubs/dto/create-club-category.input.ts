import { InputType } from '@nestjs/graphql';
import { ZodValidation } from 'src/common/zod-validator/decorators/zod-validation.decorator';
import { z } from 'zod';

const createClubCategoryInputSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  image: z.string().optional(),
  icon: z.string().optional(),
});

@ZodValidation(createClubCategoryInputSchema)
@InputType()
export class CreateClubCategoryInput {
  /** Club Category name */
  name: string;

  /** Club Category description */
  description?: string;

  /** Club Category image */
  image?: string;

  /** Club Category Icon (support lucide-react) */
  icon?: string;
}
