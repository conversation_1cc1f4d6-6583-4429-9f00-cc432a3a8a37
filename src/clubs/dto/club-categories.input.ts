import { InputType } from '@nestjs/graphql';
import { IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationInput } from 'src/common/pagination.input';

@InputType()
export class ClubCategoriesInput {
  /** Filter by name (case-insensitive partial match) */
  @IsOptional()
  @IsString()
  name?: string;

  /** Pagination options */
  @IsOptional()
  @ValidateNested()
  @Type(() => PaginationInput)
  pagination?: PaginationInput;
}
