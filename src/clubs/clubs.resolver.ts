import {
  Args,
  Context,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import * as _ from 'lodash';
import { FilterQuery } from 'mongoose';
import { GqlContext } from 'src/app.module';
import { PaginationInput } from 'src/common/pagination.input';
import { ClubsService } from './clubs.service';
import { ClubsInput } from './dto/club.input';
import { CreateClubInput } from './dto/create-club.input';
import { UpdateClubInput } from './dto/update-club.input';
import { Club, PaginatedClubs } from './entities/club.entity';
import { Public } from 'src/auth/guards/auth.guard';
import { ClubCategory } from './entities/club-categories.entity';

@Resolver(() => Club)
export class ClubsResolver {
  constructor(private readonly clubsService: ClubsService) {}

  @ResolveField(() => Club, { name: 'city' })
  getLocation(@Parent() club: Club, @Context() context: GqlContext) {
    return context.loaders.citiesLoader.load(club.city);
  }

  @ResolveField(() => Club, { name: 'neighborhood', nullable: true })
  getNeighborhood(@Parent() club: Club, @Context() context: GqlContext) {
    if (!club.neighborhood) return null;
    return context.loaders.neighborhoodsLoader.load(club.neighborhood);
  }

  @ResolveField(() => [ClubCategory], { name: 'categories' })
  getCategories(@Parent() club: Club, @Context() context: GqlContext) {
    return context.loaders.clubCategoriesLoader.loadMany(club.categories);
  }

  @Mutation(() => Club)
  createClub(@Args('createClubInput') createClubInput: CreateClubInput) {
    return this.clubsService.create(createClubInput);
  }

  @Public()
  @Query(() => PaginatedClubs, {
    name: 'clubs',
    description: 'Get all clubs with pagination and filtering',
  })
  findAllClubs(
    @Args('clubsInput', { nullable: true })
    clubsInput?: ClubsInput,
    @Args('paginationInput', { type: () => PaginationInput, nullable: true })
    paginationInput?: PaginationInput,
  ) {
    if (!clubsInput) clubsInput = {};
    else clubsInput = _.omitBy(clubsInput, (value) => _.isUndefined(value));

    const filter: FilterQuery<Club> = {};

    // Add exact match for slug if provided
    if (clubsInput.slug) filter.slug = clubsInput.slug;

    // Handle category filtering (match any of the provided categories)
    if (
      clubsInput.categories &&
      Array.isArray(clubsInput.categories) &&
      clubsInput.categories.length > 0
    ) {
      filter.categories = { $in: clubsInput.categories };
    }

    // Handle rating range filtering
    if (
      clubsInput.minRating !== undefined ||
      clubsInput.maxRating !== undefined
    ) {
      const ratingFilter: { [key: string]: number } = {};
      if (clubsInput.minRating !== undefined) {
        ratingFilter.$gte = clubsInput.minRating;
      }
      if (clubsInput.maxRating !== undefined) {
        ratingFilter.$lte = clubsInput.maxRating;
      }
      filter.rating = ratingFilter;
    }

    return this.clubsService.findAll(filter, paginationInput);
  }

  @Query(() => Club, {
    name: 'club',
    description: 'Get a single club by ID with optional filtering',
  })
  findOne(
    @Args('id', { type: () => String }) id: string,
    @Args('clubsInput', { nullable: true }) clubsInput?: ClubsInput,
  ) {
    // Start with the ID filter
    const filter: FilterQuery<Club> = { _id: id };

    // If additional filters are provided, apply them
    if (clubsInput) {
      const cleanedInput = _.omitBy(clubsInput, (value) =>
        _.isUndefined(value),
      );

      // Add slug filter if provided
      if (cleanedInput.slug) filter.slug = cleanedInput.slug;

      // Add status filter if provided
      if (cleanedInput.status) filter.status = cleanedInput.status;

      // Add city filter if provided
      if (cleanedInput.city) filter.city = cleanedInput.city;

      // Add neighborhood filter if provided
      if (cleanedInput.neighborhood)
        filter.neighborhood = cleanedInput.neighborhood;

      // Add featured filter if provided
      if (cleanedInput.featured !== undefined)
        filter.featured = cleanedInput.featured;

      // Handle category filtering (match any of the provided categories)
      if (
        cleanedInput.categories &&
        Array.isArray(cleanedInput.categories) &&
        cleanedInput.categories.length > 0
      ) {
        filter.categories = { $in: cleanedInput.categories };
      }

      // Handle rating range filtering
      if (
        cleanedInput.minRating !== undefined ||
        cleanedInput.maxRating !== undefined
      ) {
        const ratingFilter: { [key: string]: number } = {};
        if (cleanedInput.minRating !== undefined) {
          ratingFilter.$gte = cleanedInput.minRating;
        }
        if (cleanedInput.maxRating !== undefined) {
          ratingFilter.$lte = cleanedInput.maxRating;
        }
        filter.rating = ratingFilter;
      }

      // Handle contact filter if provided
      if (cleanedInput.contact) {
        filter.contact = cleanedInput.contact;
      }
    }

    return this.clubsService.findOne(filter);
  }

  @Mutation(() => Club)
  updateClub(@Args('updateClubInput') updateClubInput: UpdateClubInput) {
    return this.clubsService.update(updateClubInput.id, updateClubInput);
  }

  @Mutation(() => Club)
  removeClub(@Args('id', { type: () => String }) id: string) {
    return this.clubsService.remove(id);
  }
}
