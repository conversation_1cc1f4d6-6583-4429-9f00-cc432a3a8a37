import { ObjectType, registerEnumType, Field } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { City } from 'src/city/entities/city.entity';
import { Location, MongooseSchema } from 'src/common/common.entity';
import { Neighborhood } from 'src/neighborhood/entity/neighborhood.entity';
import { createPaginatedType } from 'src/common/pagination.input';
import GraphQLJSON from 'graphql-type-json';

export enum SectionType {
  GLOBAL = 'GLOBAL',
  LOCATION = 'LOCATION',
}

registerEnumType(SectionType, { name: 'SectionType' });

@ObjectType()
export class Section {
  /** Title of the section */
  @Field()
  @Prop({ required: true, type: String })
  title: string;

  /** Filters for the section */
  @Field(() => [GraphQLJSON])
  @Prop({ type: [Object] })
  filters: object[];

  /** Type of the section */
  @Field(() => SectionType)
  @Prop({ required: true, enum: Object.values(SectionType) })
  type: SectionType;

  /**Availability by cities */
  @Field(() => [City])
  @Prop({ type: [mongoose.Schema.Types.ObjectId], ref: City.name, default: [] })
  availableInCities: string[];

  /**Availability by neighborhoods */
  @Field(() => [Neighborhood])
  @Prop({
    type: [mongoose.Schema.Types.ObjectId],
    ref: Neighborhood.name,
    default: [],
  })
  availableInNeighborhoods: string[];

  /**Optional location */
  @Field(() => Location, { nullable: true })
  @Prop({ type: Object })
  location?: Location;

  /**if location is truthy then radius is required */
  @Field({ nullable: true })
  @Prop({ type: Number, default: 30 * 1000 })
  radius: number;
}

@ObjectType()
@Schema()
export class AppLayout extends MongooseSchema {
  /** Name of the app layout */
  @Field()
  @Prop({ required: true })
  name: string;

  /** Sections in the app layout */
  @Field(() => [Section])
  @Prop({ type: [Object] })
  sections: Section[];

  /** Whether the app layout is active (only one applayout can be active) */
  @Field()
  @Prop({ default: false })
  active: boolean;
}

@ObjectType()
export class PaginatedAppLayouts extends createPaginatedType(AppLayout) {}

export const AppLayoutSchema = SchemaFactory.createForClass(AppLayout);
