import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { NeighborhoodService } from './neighborhood.service';
import {
  Neighborhood,
  PaginatedNeighborhoods,
} from './entity/neighborhood.entity';
import { CreateNeighborhoodInput } from './dto/create-neighborhood.input';
import { UpdateNeighborhoodInput } from './dto/update-neighborhood.input';
import { NeighborhoodsInput } from './dto/neighborhoods.input';
import { PaginationInput } from 'src/common/pagination.input';
import * as _ from 'lodash';

@Resolver(() => Neighborhood)
export class NeighborhoodResolver {
  constructor(private readonly neighborhoodService: NeighborhoodService) {}

  @Mutation(() => Neighborhood, { description: 'Create a new neighborhood' })
  createNeighborhood(
    @Args('createNeighborhoodInput')
    createNeighborhoodInput: CreateNeighborhoodInput,
  ): Promise<Neighborhood> {
    return this.neighborhoodService.create(createNeighborhoodInput);
  }

  @Query(() => PaginatedNeighborhoods, {
    name: 'neighborhoods',
    description: 'Get all neighborhoods with pagination and filtering',
  })
  findAllNeighborhoods(
    @Args('neighborhoodsInput', { nullable: true })
    neighborhoodsInput?: NeighborhoodsInput,
    @Args('paginationInput', { type: () => PaginationInput, nullable: true })
    paginationInput?: PaginationInput,
  ) {
    const filter = _.omitBy(_.omit(neighborhoodsInput, 'pagination'), (value) =>
      _.isUndefined(value),
    );

    // Add regex search for name if provided
    if (filter.name) {
      (filter.name as any) = { $regex: new RegExp(filter.name, 'i') };
    }

    return this.neighborhoodService.findAll(
      filter,
      paginationInput || neighborhoodsInput?.pagination,
    );
  }

  @Query(() => Neighborhood, {
    name: 'neighborhood',
    description: 'Get a single neighborhood by ID',
  })
  findNeighborhoodById(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<Neighborhood> {
    return this.neighborhoodService.findOne(id);
  }

  @Mutation(() => Neighborhood, {
    description: 'Update an existing neighborhood',
  })
  updateNeighborhood(
    @Args('updateNeighborhoodInput')
    updateNeighborhoodInput: UpdateNeighborhoodInput,
  ): Promise<Neighborhood> {
    return this.neighborhoodService.update(
      updateNeighborhoodInput.id,
      updateNeighborhoodInput,
    );
  }

  @Mutation(() => Neighborhood, { description: 'Delete a neighborhood' })
  deleteNeighborhood(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<Neighborhood> {
    return this.neighborhoodService.remove(id);
  }
}
