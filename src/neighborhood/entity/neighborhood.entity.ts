import { ObjectType, Field } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { Location, MongooseSchema } from 'src/common/common.entity';
import { City } from 'src/city/entities/city.entity';
import { createPaginatedType } from 'src/common/pagination.input';

@ObjectType()
@Schema()
export class Neighborhood extends MongooseSchema {
  /** Name of the neighborhood */
  @Prop({ required: true, index: true })
  name: string;

  /** SEO-friendly URL slug for the neighborhood */
  @Field()
  @Prop({ required: true, unique: true, index: true })
  slug: string;

  /** Geographical location of the neighborhood */
  @Prop({ required: true, type: Object })
  location: Location;

  /** Reference to the city this neighborhood belongs to */
  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId, ref: 'City' })
  @Field(() => City)
  city: City;

  /** Main cover image of the neighborhood */
  @Prop()
  coverImage?: string;

  /** Array of neighborhood image URLs */
  @Prop({ type: [String] })
  images?: string[];
}

@ObjectType()
export class PaginatedNeighborhoods extends createPaginatedType(Neighborhood) {}

export const NeighborhoodSchema = SchemaFactory.createForClass(Neighborhood);

// Create 2dsphere index on location.center for geospatial queries
NeighborhoodSchema.index({ 'location.center': '2dsphere' });
