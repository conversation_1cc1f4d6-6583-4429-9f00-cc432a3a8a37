import { InputType, Field } from '@nestjs/graphql';
import { LocationInput } from 'src/common/common.entity';
import { ZodValidation } from 'src/common/zod-validator/decorators/zod-validation.decorator';
import { z } from 'zod';

const createNeighborhoodInputSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  slug: z
    .string()
    .min(3, 'Slug must be at least 3 characters long')
    .max(100, 'Slug must not exceed 100 characters')
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      'Slug must be lowercase, contain only letters, numbers, and hyphens, and not start or end with a hyphen',
    ),
  location: z.object({
    center: z.array(z.number()).length(2),
    coords: z.array(z.array(z.number()).length(2)).optional(),
  }),
  city: z.string().min(1, 'City ID is required'),
  coverImage: z.string().optional(),
  images: z.array(z.string()).optional(),
});

@ZodValidation(createNeighborhoodInputSchema)
@InputType()
export class CreateNeighborhoodInput {
  /** Name of the neighborhood */
  name: string;

  /** SEO-friendly URL slug for the neighborhood */
  @Field()
  slug: string;

  /** Geographical location of the neighborhood */
  location: LocationInput;

  /** City ID this neighborhood belongs to */
  city: string;

  /** Main cover image of the neighborhood */
  coverImage?: string;

  /** Array of neighborhood image URLs */
  images?: string[];
}
