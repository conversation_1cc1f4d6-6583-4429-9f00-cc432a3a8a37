import { InputType, Field } from '@nestjs/graphql';
import { PaginationInput } from 'src/common/pagination.input';
import { IsOptional, IsString } from 'class-validator';

@InputType()
export class NeighborhoodsInput {
  /** Filter by neighborhood name */
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  name?: string;

  /** Filter by city ID */
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  city?: string;

  /** Pagination options */
  @Field(() => PaginationInput, { nullable: true })
  @IsOptional()
  pagination?: PaginationInput;
}
