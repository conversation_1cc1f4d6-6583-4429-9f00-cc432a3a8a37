import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, PaginateModel } from 'mongoose';
import { PaginationInput } from 'src/common/pagination.input';
import { CreateNeighborhoodInput } from './dto/create-neighborhood.input';
import { UpdateNeighborhoodInput } from './dto/update-neighborhood.input';
import { Neighborhood } from './entity/neighborhood.entity';

@Injectable()
export class NeighborhoodService {
  constructor(
    @InjectModel(Neighborhood.name)
    private neighborhoodModel: PaginateModel<Neighborhood>,
  ) {}

  async create(createNeighborhoodInput: CreateNeighborhoodInput) {
    // Check if neighborhood with same name already exists in the same city
    const existingNeighborhood = await this.neighborhoodModel.findOne({
      name: { $regex: new RegExp(`^${createNeighborhoodInput.name}$`, 'i') },
      city: createNeighborhoodInput.city,
    });

    if (existingNeighborhood) {
      throw new ConflictException(
        'Neighborhood with this name already exists in this city',
      );
    }

    // Check if slug already exists
    const existingSlug = await this.neighborhoodModel.findOne({
      slug: createNeighborhoodInput.slug,
    });

    if (existingSlug) {
      throw new ConflictException('Neighborhood with this slug already exists');
    }

    return this.neighborhoodModel.create(createNeighborhoodInput);
  }

  async findAll(
    filter: FilterQuery<Neighborhood> = {},
    paginationInput?: PaginationInput,
  ) {
    return this.neighborhoodModel.paginate(filter, {
      ...paginationInput,
      populate: 'city',
    });
  }

  async findOne(id: string): Promise<Neighborhood> {
    const neighborhood = await this.neighborhoodModel
      .findById(id)
      .populate('city');

    if (!neighborhood) {
      throw new NotFoundException(`Neighborhood with ID ${id} not found`);
    }

    return neighborhood;
  }

  async update(
    id: string,
    updateNeighborhoodInput: UpdateNeighborhoodInput,
  ): Promise<Neighborhood> {
    // Check if neighborhood exists
    const existingNeighborhood = await this.neighborhoodModel.findById(id);
    if (!existingNeighborhood) {
      throw new NotFoundException(`Neighborhood with ID ${id} not found`);
    }

    // Check if name is being updated and if it conflicts with another neighborhood in the same city
    if (
      updateNeighborhoodInput.name &&
      updateNeighborhoodInput.name !== existingNeighborhood.name
    ) {
      const cityId = updateNeighborhoodInput.city || existingNeighborhood.city;
      const nameConflict = await this.neighborhoodModel.findOne({
        name: {
          $regex: new RegExp(`^${updateNeighborhoodInput.name}$`, 'i'),
        },
        city: cityId,
        _id: { $ne: id },
      });

      if (nameConflict) {
        throw new ConflictException(
          'Neighborhood with this name already exists in this city',
        );
      }
    }

    // Check if slug is being updated and if it conflicts with another neighborhood
    if (
      updateNeighborhoodInput.slug &&
      updateNeighborhoodInput.slug !== existingNeighborhood.slug
    ) {
      const slugConflict = await this.neighborhoodModel.findOne({
        slug: updateNeighborhoodInput.slug,
        _id: { $ne: id },
      });

      if (slugConflict) {
        throw new ConflictException(
          'Neighborhood with this slug already exists',
        );
      }
    }

    const updatedNeighborhood = await this.neighborhoodModel
      .findByIdAndUpdate(id, updateNeighborhoodInput, { new: true })
      .populate('city');

    return updatedNeighborhood!;
  }

  async remove(id: string): Promise<Neighborhood> {
    const neighborhood = await this.neighborhoodModel
      .findByIdAndDelete(id)
      .populate('city');

    if (!neighborhood) {
      throw new NotFoundException(`Neighborhood with ID ${id} not found`);
    }

    return neighborhood;
  }
}
