import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { NeighborhoodService } from './neighborhood.service';
import { NeighborhoodResolver } from './neighborhood.resolver';
import { Neighborhood, NeighborhoodSchema } from './entity/neighborhood.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Neighborhood.name, schema: NeighborhoodSchema },
    ]),
  ],
  providers: [NeighborhoodResolver, NeighborhoodService],
  exports: [NeighborhoodService],
})
export class NeighborhoodModule {}
