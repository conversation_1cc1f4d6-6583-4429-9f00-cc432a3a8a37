import {
  Args,
  Context,
  ID,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import * as _ from 'lodash';
import { GqlContext } from 'src/app.module';
import { City } from 'src/city/entities/city.entity';
import { PaginationInput } from 'src/common/pagination.input';
import { Neighborhood } from 'src/neighborhood/entity/neighborhood.entity';
import { BarsService } from './bars.service';
import { BarsInput } from './dto/bars.input';
import { CreateBarInput } from './dto/create-bar.input';
import { UpdateBarInput } from './dto/update-bar.input';
import { BarCategory } from './entities/bar-categories.entity';
import { Bar, PaginatedBars } from './entities/bar.entity';
import { FilterQuery } from 'mongoose';
import {
  createNearQuery,
  createWithinRadiusQuery,
  createBoundingBoxQuery,
} from 'src/common/geospatial.utils';

@Resolver(() => Bar)
export class BarsResolver {
  constructor(private readonly barsService: BarsService) {}

  @ResolveField(() => City, { name: 'city' })
  getLocation(@Parent() bar: Bar, @Context() context: GqlContext) {
    return context.loaders.citiesLoader.load(bar.city);
  }

  @ResolveField(() => Neighborhood, { name: 'neighborhood', nullable: true })
  getNeighborhood(@Parent() bar: Bar, @Context() context: GqlContext) {
    if (!bar.neighborhood) return null;
    return context.loaders.neighborhoodsLoader.load(bar.neighborhood);
  }

  @ResolveField(() => [BarCategory], { name: 'categories' })
  getCategories(@Parent() bar: Bar, @Context() context: GqlContext) {
    return context.loaders.barCategoriesLoader.loadMany(bar.categories);
  }

  @Mutation(() => Bar, { description: 'Create a new bar' })
  createBar(@Args('createBarInput') createBarInput: CreateBarInput) {
    return this.barsService.create(createBarInput);
  }

  @Query(() => PaginatedBars, {
    name: 'bars',
    description: 'Get all bars with pagination and filtering',
  })
  findAllBars(
    @Args('barsInput', { nullable: true })
    barsInput?: BarsInput,
    @Args('paginationInput', { type: () => PaginationInput, nullable: true })
    paginationInput?: PaginationInput,
  ) {
    if (!barsInput) barsInput = {};
    else barsInput = _.omitBy(barsInput, (value) => _.isUndefined(value));

    const filter: FilterQuery<Bar> = {};

    // Add search for slug if provided
    if (barsInput.slug) filter.slug = barsInput.slug;

    // Add status filter
    if (barsInput.featured) filter.featured = barsInput.featured;

    // ADd city filter
    if (barsInput.city) filter.city = barsInput.city;

    // Handle category filtering (match any of the provided categories)
    if (
      barsInput.categories &&
      Array.isArray(barsInput.categories) &&
      barsInput.categories.length > 0
    ) {
      filter.categories = { $in: barsInput.categories };
    }

    // Handle rating range filtering
    if (
      barsInput.minRating !== undefined ||
      barsInput.maxRating !== undefined
    ) {
      const ratingFilter: { [key: string]: number } = {};
      if (barsInput.minRating !== undefined) {
        ratingFilter.$gte = barsInput.minRating;
      }
      if (barsInput.maxRating !== undefined) {
        ratingFilter.$lte = barsInput.maxRating;
      }
      filter.rating = ratingFilter;
    }

    // Handle geospatial queries
    if (barsInput.nearLocation) {
      const nearQuery = createNearQuery(
        barsInput.nearLocation.longitude,
        barsInput.nearLocation.latitude,
        barsInput.nearLocation.maxDistance,
      );
      Object.assign(filter, nearQuery);
    } else if (barsInput.withinRadius) {
      const radiusQuery = createWithinRadiusQuery(
        barsInput.withinRadius.longitude,
        barsInput.withinRadius.latitude,
        barsInput.withinRadius.radiusMeters,
      );
      Object.assign(filter, radiusQuery);
    } else if (barsInput.withinBounds) {
      const boundsQuery = createBoundingBoxQuery(
        barsInput.withinBounds.southWestLng,
        barsInput.withinBounds.southWestLat,
        barsInput.withinBounds.northEastLng,
        barsInput.withinBounds.northEastLat,
      );
      Object.assign(filter, boundsQuery);
    }

    return this.barsService.findAll(filter, paginationInput);
  }

  @Query(() => Bar, {
    name: 'bar',
    description: 'Get a single bar by ID with optional filtering',
  })
  findBarById(
    @Args('id', { type: () => ID }) id: string,
    @Args('barsInput', { nullable: true }) barsInput?: BarsInput,
  ) {
    // Start with the ID filter
    const filter: FilterQuery<Bar> = { _id: id };

    // If additional filters are provided, apply them
    if (barsInput) {
      const cleanedInput = _.omitBy(barsInput, (value) => _.isUndefined(value));

      // Add slug filter if provided
      if (cleanedInput.slug) filter.slug = cleanedInput.slug;

      // Add status filter if provided
      if (cleanedInput.status) filter.status = cleanedInput.status;

      // Add city filter if provided
      if (cleanedInput.city) filter.city = cleanedInput.city;

      // Add neighborhood filter if provided
      if (cleanedInput.neighborhood)
        filter.neighborhood = cleanedInput.neighborhood;

      // Add featured filter if provided
      if (cleanedInput.featured !== undefined)
        filter.featured = cleanedInput.featured;

      // Handle category filtering (match any of the provided categories)
      if (
        cleanedInput.categories &&
        Array.isArray(cleanedInput.categories) &&
        cleanedInput.categories.length > 0
      ) {
        filter.categories = { $in: cleanedInput.categories };
      }

      // Handle rating range filtering
      if (
        cleanedInput.minRating !== undefined ||
        cleanedInput.maxRating !== undefined
      ) {
        const ratingFilter: { [key: string]: number } = {};
        if (cleanedInput.minRating !== undefined) {
          ratingFilter.$gte = cleanedInput.minRating;
        }
        if (cleanedInput.maxRating !== undefined) {
          ratingFilter.$lte = cleanedInput.maxRating;
        }
        filter.rating = ratingFilter;
      }

      // Handle geospatial queries
      if (cleanedInput.nearLocation) {
        const nearQuery = createNearQuery(
          cleanedInput.nearLocation.longitude,
          cleanedInput.nearLocation.latitude,
          cleanedInput.nearLocation.maxDistance,
        );
        Object.assign(filter, nearQuery);
      } else if (cleanedInput.withinRadius) {
        const radiusQuery = createWithinRadiusQuery(
          cleanedInput.withinRadius.longitude,
          cleanedInput.withinRadius.latitude,
          cleanedInput.withinRadius.radiusMeters,
        );
        Object.assign(filter, radiusQuery);
      } else if (cleanedInput.withinBounds) {
        const boundsQuery = createBoundingBoxQuery(
          cleanedInput.withinBounds.southWestLng,
          cleanedInput.withinBounds.southWestLat,
          cleanedInput.withinBounds.northEastLng,
          cleanedInput.withinBounds.northEastLat,
        );
        Object.assign(filter, boundsQuery);
      }
    }

    return this.barsService.findOne(filter);
  }

  @Mutation(() => Bar, { description: 'Update an existing bar' })
  updateBar(@Args('updateBarInput') updateBarInput: UpdateBarInput) {
    return this.barsService.update(updateBarInput.id, updateBarInput);
  }

  @Mutation(() => Bar, { description: 'Delete a bar' })
  deleteBar(@Args('id', { type: () => ID }) id: string) {
    return this.barsService.remove(id);
  }
}
