import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BarsService } from './bars.service';
import { BarsResolver } from './bars.resolver';
import { BarCategoriesService } from './bar-categories.service';
import { BarCategoriesResolver } from './bar-categories.resolver';
import { Bar, BarSchema } from './entities/bar.entity';
import {
  BarCategory,
  BarCategorySchema,
} from './entities/bar-categories.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Bar.name, schema: BarSchema },
      { name: BarCategory.name, schema: BarCategorySchema },
    ]),
  ],
  providers: [
    BarsResolver,
    BarsService,
    BarCategoriesResolver,
    BarCategoriesService,
  ],
  exports: [BarsService, BarCategoriesService],
})
export class BarsModule {}
