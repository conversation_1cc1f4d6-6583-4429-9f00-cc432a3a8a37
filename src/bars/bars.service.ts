import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { PaginateModel, FilterQuery } from 'mongoose';
import { CreateBarInput } from './dto/create-bar.input';
import { UpdateBarInput } from './dto/update-bar.input';
import { Bar } from './entities/bar.entity';
import { PaginationInput } from 'src/common/pagination.input';

@Injectable()
export class BarsService {
  constructor(
    @InjectModel(Bar.name)
    private bar: PaginateModel<Bar>,
  ) {}

  async create(createBarInput: CreateBarInput) {
    return this.bar.create(createBarInput);
  }

  async findAll(
    filter: FilterQuery<Bar> = {},
    paginationInput?: PaginationInput,
  ) {
    return this.bar.paginate(filter, paginationInput);
  }

  async findOne(filter: FilterQuery<Bar>) {
    return this.bar.findOne(filter);
  }

  async update(id: string, updateBarInput: UpdateBarInput) {
    return this.bar.findByIdAndUpdate(id, updateBarInput, { new: true });
  }

  async remove(id: string) {
    return this.bar.findByIdAndDelete(id);
  }

  async count(filter: FilterQuery<Bar> = {}): Promise<number> {
    return this.bar.countDocuments(filter);
  }
}
