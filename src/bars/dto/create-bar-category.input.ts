import { InputType, Field } from '@nestjs/graphql';
import { ZodValidation } from 'src/common/zod-validator/decorators/zod-validation.decorator';
import { z } from 'zod';

const createBarCategoryInputSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  image: z.string().optional(),
  icon: z.string().optional(),
});

@ZodValidation(createBarCategoryInputSchema)
@InputType()
export class CreateBarCategoryInput {
  /** Bar Category name */
  @Field()
  name: string;

  /** Bar Category description */
  @Field({ nullable: true })
  description?: string;

  /** Bar Category image */
  @Field({ nullable: true })
  image?: string;

  /** Bar Category Icon (support lucide-react) */
  @Field({ nullable: true })
  icon?: string;
}
