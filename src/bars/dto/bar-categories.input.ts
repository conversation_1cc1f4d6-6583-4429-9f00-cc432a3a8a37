import { InputType, Field } from '@nestjs/graphql';
import { IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationInput } from 'src/common/pagination.input';

@InputType()
export class BarCategoriesInput {
  /** Filter by name (case-insensitive partial match) */
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  name?: string;

  /** Pagination options */
  @Field(() => PaginationInput, { nullable: true })
  @IsOptional()
  @ValidateNested()
  @Type(() => PaginationInput)
  pagination?: PaginationInput;
}
