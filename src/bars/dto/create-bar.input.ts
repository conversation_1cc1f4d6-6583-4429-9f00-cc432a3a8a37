import { InputType, Field } from '@nestjs/graphql';
import {
  AddressInput,
  DayOfWeek,
  DayTimingInput,
  LocationInput,
} from 'src/common/common.entity';
import { ZodValidation } from 'src/common/zod-validator/decorators/zod-validation.decorator';
import { ContactInput } from 'src/users/dto/create-user.input';
import { z } from 'zod';
import { BarStatus, BarMenuItemAvailability } from '../entities/bar.entity';

const createBarInputSchema = z.object({
  name: z.string(),
  slug: z
    .string()
    .min(3, 'Slug must be at least 3 characters long')
    .max(100, 'Slug must not exceed 100 characters')
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      'Slug must be lowercase, contain only letters, numbers, and hyphens, and not start or end with a hyphen',
    ),
  description: z.string(),
  categories: z.array(z.string()),
  status: z.nativeEnum(BarStatus),
  city: z.string(),
  neighborhood: z.string().optional(),
  location: z.object({
    center: z.array(z.number()).length(2),
    coords: z.array(z.array(z.number()).length(2)).optional(),
  }),
  logo: z.string().optional(),
  coverImage: z.string().optional(),
  images: z.array(z.string()),
  phone: z
    .object({
      countryCode: z.string(),
      phone: z.string(),
    })
    .optional(),
  address: z
    .object({
      address: z.string(),
      location: z.object({
        center: z.array(z.number()).length(2),
        coords: z.array(z.array(z.number()).length(2)).optional(),
      }),
      metroLine: z.string().optional(),
      metroStation: z.string().optional(),
    })
    .optional(),
  businessHours: z
    .object({
      schedule: z.array(
        z.object({
          day: z.nativeEnum(DayOfWeek),
          timings: z.array(z.number()).min(1).max(2),
        }),
      ),
    })
    .optional(),
  happyHours: z
    .object({
      schedule: z.array(
        z.object({
          day: z.nativeEnum(DayOfWeek),
          timings: z.array(z.number()).min(1).max(2),
        }),
      ),
    })
    .optional(),
  menu: z
    .object({
      currency: z.string(),
      sections: z.array(
        z.object({
          name: z.string(),
          items: z.array(
            z.object({
              name: z.string(),
              price: z.number().min(0),
              available: z.nativeEnum(BarMenuItemAvailability),
            }),
          ),
        }),
      ),
    })
    .optional(),
  featured: z.boolean(),
  rating: z.number().min(0).max(5),
});

@InputType()
export class BarBusinessHoursInput {
  /** Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format */
  @Field(() => [DayTimingInput])
  schedule: DayTimingInput[];
}

@InputType()
export class BarHappyHoursInput {
  /** Array of happy hour day timings. Each day can have start time only or both start and end times in 24hr HHMM format */
  @Field(() => [DayTimingInput])
  schedule: DayTimingInput[];
}

@InputType()
export class BarMenuItemInput {
  /** Menu item name */
  @Field()
  name: string;

  /** Menu item price */
  @Field()
  price: number;

  /** Whether the item is available */
  @Field(() => BarMenuItemAvailability)
  available: BarMenuItemAvailability;
}

@InputType()
export class BarMenuSectionInput {
  /** Section name (e.g., "Bières (pression)50cl", "Pizza", "Plats") */
  @Field()
  name: string;

  /** Array of menu items in this section */
  @Field(() => [BarMenuItemInput])
  items: BarMenuItemInput[];
}

@InputType()
export class BarMenuInput {
  /** Menu currency (e.g., USD, EUR, INR) */
  @Field()
  currency: string;

  /** Array of menu sections */
  @Field(() => [BarMenuSectionInput])
  sections: BarMenuSectionInput[];
}

@ZodValidation(createBarInputSchema)
@InputType()
export class CreateBarInput {
  /** Bar name */
  @Field()
  name: string;

  /** SEO-friendly URL slug for the bar */
  @Field()
  slug: string;

  /** Bar description */
  @Field()
  description: string;

  /** Bar city IDs */
  @Field()
  city: string;

  /** Optional neighborhood ID */
  @Field({ nullable: true })
  neighborhood?: string;

  /** Geographical location of the bar */
  @Field(() => LocationInput)
  location: LocationInput;

  /** Bar category IDs */
  @Field(() => [String])
  categories: string[];

  /** Bar status */
  @Field(() => BarStatus)
  status: BarStatus;

  /** Bar logo URL */
  @Field({ nullable: true })
  logo?: string;

  /** Bar cover image URL */
  @Field({ nullable: true })
  coverImage?: string;

  /** Array of bar image URLs */
  @Field(() => [String])
  images: string[];

  /** Bar contact information (phone) */
  @Field(() => ContactInput, { nullable: true })
  contact?: ContactInput;

  /** Bar address */
  @Field(() => AddressInput, { nullable: true })
  address?: AddressInput;

  /** Opening hours */
  @Field(() => BarBusinessHoursInput, { nullable: true })
  businessHours?: BarBusinessHoursInput;

  /** Happy hours schedule */
  @Field(() => BarHappyHoursInput, { nullable: true })
  happyHours?: BarHappyHoursInput;

  /** Bar menu */
  @Field(() => BarMenuInput, { nullable: true })
  menu?: BarMenuInput;

  /** Whether bar is featured */
  @Field()
  featured: boolean;

  /** Bar rating (0-5) */
  @Field()
  rating: number;
}
