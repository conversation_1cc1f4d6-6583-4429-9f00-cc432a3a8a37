import { InputType, Field } from '@nestjs/graphql';
import {
  IsOptional,
  IsString,
  IsArray,
  IsBoolean,
  IsN<PERSON>ber,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { BarStatus } from '../entities/bar.entity';
import {
  LocationQueryInput,
  RadiusQueryInput,
  BoundingBoxInput,
} from 'src/common/geospatial.input';

@InputType()
export class BarsInput {
  /** Filter by bar slug */
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  slug?: string;

  /** Filter by bar status */
  @Field(() => BarStatus, { nullable: true })
  @IsOptional()
  status?: BarStatus;

  /** Filter by city ID */
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  city?: string;

  /** Filter by neighborhood ID */
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  neighborhood?: string;

  /** Filter by category IDs */
  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  categories?: string[];

  /** Filter by featured status */
  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  /** Filter by minimum rating */
  @Field({ nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  minRating?: number;

  /** Filter by maximum rating */
  @Field({ nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  maxRating?: number;

  /** Filter by location proximity - sort by nearest to given coordinates */
  @Field(() => LocationQueryInput, { nullable: true })
  @IsOptional()
  nearLocation?: LocationQueryInput;

  /** Filter by radius - find bars within specified radius */
  @Field(() => RadiusQueryInput, { nullable: true })
  @IsOptional()
  withinRadius?: RadiusQueryInput;

  /** Filter by bounding box - find bars within specified area */
  @Field(() => BoundingBoxInput, { nullable: true })
  @IsOptional()
  withinBounds?: BoundingBoxInput;
}
