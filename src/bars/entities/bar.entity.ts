import { Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { City } from 'src/city/entities/city.entity';
import {
  Address,
  DayOfWeek,
  Location,
  MongooseSchema,
} from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.input';
import { Neighborhood } from 'src/neighborhood/entity/neighborhood.entity';
import { Contact } from 'src/users/entities/user.entity';
import { BarCategory } from './bar-categories.entity';

export enum BarStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  SUSPENDED = 'SUSPENDED',
}

registerEnumType(BarStatus, { name: 'BarStatus' });

@ObjectType()
export class BarDayTiming {
  /** Day of the week */
  @Field(() => DayOfWeek)
  day: DayOfWeek;

  @Field(() => [Number])
  /** Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30) */
  timings: [number, number];
}

@ObjectType()
export class BarBusinessHoursSchedule {
  /** Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format */
  @Field(() => [BarDayTiming])
  schedule: BarDayTiming[];
}

@ObjectType()
export class BarHappyHoursSchedule {
  /** Array of happy hour day timings. Each day can have start time only or both start and end times in 24hr HHMM format */
  @Field(() => [BarDayTiming])
  schedule: BarDayTiming[];
}

export enum BarMenuItemAvailability {
  AVAILABLE = 'AVAILABLE',
  UNAVAILABLE = 'UNAVAILABLE',
}
registerEnumType(BarMenuItemAvailability, { name: 'BarMenuItemAvailability' });

@ObjectType()
export class BarMenuItem {
  /** Menu item name */
  @Field()
  name: string;

  /** Menu item price */
  @Field()
  price: number;

  /** Whether the item is available */
  @Field(() => BarMenuItemAvailability)
  available: BarMenuItemAvailability;
}

@ObjectType()
export class BarMenuSection {
  /** Section name (e.g., "Bières (pression)50cl", "Pizza", "Plats") */
  @Field()
  name: string;

  /** Array of menu items in this section */
  @Field(() => [BarMenuItem])
  items: BarMenuItem[];
}

@ObjectType()
export class BarMenu {
  /** Menu currency (e.g., USD, EUR, INR) */
  @Field()
  currency: string;

  /** Array of menu sections */
  @Field(() => [BarMenuSection])
  sections: BarMenuSection[];
}

@ObjectType()
@Schema()
export class Bar extends MongooseSchema {
  /** Bar name */
  @Field()
  @Prop({ required: true })
  name: string;

  /** SEO-friendly URL slug for the bar */
  @Field()
  @Prop({ required: true, unique: true, index: true })
  slug: string;

  /** Bar description */
  @Field()
  @Prop({ required: true })
  description: string;

  /** Bar category */
  @Prop({ required: true, type: [mongoose.Schema.Types.ObjectId] })
  @Field(() => [BarCategory])
  categories: [string];

  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId })
  @Field(() => City)
  city: string;

  /** Optional neighborhood reference */
  @Prop({ type: mongoose.Schema.Types.ObjectId })
  @Field(() => Neighborhood, { nullable: true })
  neighborhood?: string;

  /** Bar status */
  @Field(() => BarStatus)
  @Prop({
    required: true,
    enum: Object.values(BarStatus),
    default: BarStatus.PENDING,
  })
  status: BarStatus;

  /** Bar logo URL */
  @Field({ nullable: true })
  @Prop()
  logo?: string;

  /** Bar cover image URL */
  @Field({ nullable: true })
  @Prop()
  coverImage?: string;

  /** Array of bar image URLs */
  @Field(() => [String])
  @Prop({ required: true, type: [String] })
  images: string[];

  /** Bar phone number */
  @Field(() => Contact, { nullable: true })
  @Prop({ type: Object })
  contact?: Contact;

  /** Bar address */
  @Field(() => Address, { nullable: true })
  @Prop({ type: Object })
  address?: Address;

  /** Opening hours */
  @Field(() => BarBusinessHoursSchedule, { nullable: true })
  @Prop({ type: Object })
  businessHours?: BarBusinessHoursSchedule;

  /** Happy hours schedule */
  @Field(() => BarHappyHoursSchedule, { nullable: true })
  @Prop({ type: Object })
  happyHours?: BarHappyHoursSchedule;

  /** Bar menu */
  @Field(() => BarMenu, { nullable: true })
  @Prop({ type: Object })
  menu?: BarMenu;

  /** Whether bar is featured */
  @Field()
  @Prop({ default: false })
  featured: boolean;

  /** Bar rating (0-5) */
  @Field()
  @Prop({ default: 0, min: 0, max: 5 })
  rating: number;

  /** Geographical location of the bar */
  @Field(() => Location, { nullable: true })
  @Prop({ type: Object })
  location?: Location;
}

export const BarSchema = SchemaFactory.createForClass(Bar);

// Create 2dsphere index on location.center for geospatial queries
BarSchema.index({ 'location.center': '2dsphere' });

@ObjectType()
export class PaginatedBars extends createPaginatedType(Bar) {}
