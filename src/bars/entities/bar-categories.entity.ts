import { ObjectType, Field } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.input';

@ObjectType()
@Schema()
export class BarCategory extends MongooseSchema {
  /** Bar Category name */
  @Field()
  @Prop({ required: true, index: true, unique: true })
  name: string;

  /** Bar Category description */
  @Field({ nullable: true })
  @Prop({ required: false })
  description?: string;

  /** Bar Category image */
  @Field({ nullable: true })
  @Prop({ required: false })
  image?: string;

  /** Bar Category Icon (support lucide-react) */
  @Field({ nullable: true })
  @Prop({ required: false })
  icon?: string;
}

export const BarCategorySchema = SchemaFactory.createForClass(BarCategory);

@ObjectType()
export class PaginatedBarCategories extends createPaginatedType(BarCategory) {}
