import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CityService } from './city.service';
import { CityResolver } from './city.resolver';
import { City, CitySchema } from './entities/city.entity';
import { BarsModule } from 'src/bars/bars.module';
import { ClubsModule } from 'src/clubs/clubs.module';
import { NeighborhoodModule } from 'src/neighborhood/neighborhood.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: City.name, schema: CitySchema }]),
    BarsModule,
    ClubsModule,
    NeighborhoodModule,
  ],
  providers: [CityResolver, CityService],
  exports: [CityService],
})
export class CityModule {}
