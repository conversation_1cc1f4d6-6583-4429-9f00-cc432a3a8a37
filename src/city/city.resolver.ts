import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { CityService } from './city.service';
import {
  City,
  PaginatedCities,
  NeighborhoodStats,
} from './entities/city.entity';
import { CreateCityInput } from './dto/create-city.input';
import { UpdateCityInput } from './dto/update-city.input';
import { CitiesInput } from './dto/cities.input';
import { PaginationInput } from 'src/common/pagination.input';
import * as _ from 'lodash';

@Resolver(() => City)
export class CityResolver {
  constructor(private readonly cityService: CityService) {}

  @Mutation(() => City, { description: 'Create a new city' })
  createCity(
    @Args('createCityInput') createCityInput: CreateCityInput,
  ): Promise<City> {
    return this.cityService.create(createCityInput);
  }

  @Query(() => PaginatedCities, {
    name: 'cities',
    description: 'Get all cities with pagination and filtering',
  })
  findAllCities(
    @Args('citiesInput', { nullable: true }) citiesInput?: CitiesInput,
    @Args('paginationInput', { type: () => PaginationInput, nullable: true })
    paginationInput?: PaginationInput,
  ) {
    return this.cityService.findAll(
      _.omitBy(citiesInput, (value) => _.isUndefined(value)),
      paginationInput,
    );
  }

  @Query(() => City, {
    name: 'city',
    description: 'Get a single city by ID',
  })
  findCityById(@Args('id', { type: () => ID }) id: string): Promise<City> {
    return this.cityService.findOne(id);
  }

  @Mutation(() => City, { description: 'Update an existing city' })
  updateCity(
    @Args('updateCityInput') updateCityInput: UpdateCityInput,
  ): Promise<City> {
    return this.cityService.update(updateCityInput.id, updateCityInput);
  }

  @Mutation(() => City, { description: 'Delete a city' })
  deleteCity(@Args('id', { type: () => ID }) id: string): Promise<City> {
    return this.cityService.remove(id);
  }

  @Query(() => [NeighborhoodStats], {
    name: 'cityNeighborhoodStats',
    description:
      'Get neighborhoods with bar and club counts for a specific city',
  })
  getCityNeighborhoodStats(
    @Args('cityId', { type: () => ID }) cityId: string,
  ): Promise<NeighborhoodStats[]> {
    return this.cityService.getNeighborhoodStats(cityId);
  }
}
