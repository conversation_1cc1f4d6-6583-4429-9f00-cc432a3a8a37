import { InputType } from '@nestjs/graphql';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { LocationInput } from 'src/common/common.entity';

@InputType()
export class CreateCityInput {
  /** Name of the city */
  @IsNotEmpty()
  @IsString()
  name: string;

  /** Main image of the city */
  @IsNotEmpty()
  @IsString()
  image: string;

  /** Cover image for the city */
  @IsOptional()
  @IsString()
  coverImage?: string;

  /** Main heading for the city */
  @IsNotEmpty()
  @IsString()
  heading: string;

  /** Sub heading for the city */
  @IsOptional()
  @IsString()
  subHeading?: string;

  /** Geographical location of the city */
  @ValidateNested()
  @Type(() => LocationInput)
  location: LocationInput;
}
