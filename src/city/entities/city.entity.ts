import { ObjectType, Field } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Location, MongooseSchema } from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.input';

@ObjectType()
@Schema()
export class City extends MongooseSchema {
  /** Name of the city */
  @Prop({ required: true, index: true })
  name: string;

  /** Main image of the city */
  @Prop({ required: true })
  image: string;

  /** Cover image for the city */
  @Prop()
  coverImage?: string;

  /** Main heading for the city */
  @Prop({ required: true })
  heading: string;

  /** Sub heading for the city */
  @Prop()
  subHeading?: string;

  /** Geographical location of the city */
  @Prop({ required: true, type: Object })
  location: Location;
}

@ObjectType()
export class NeighborhoodStats {
  /** Neighborhood ID */
  @Field()
  id: string;

  /** Neighborhood name */
  @Field()
  name: string;

  /** Neighborhood slug */
  @Field()
  slug: string;

  /** Cover image for the neighborhood */
  @Field({ nullable: true })
  coverImage?: string;

  /** Array of neighborhood image URLs */
  @Field(() => [String], { nullable: true })
  images?: string[];

  /** Total number of bars in this neighborhood */
  @Field()
  totalBars: number;

  /** Total number of clubs in this neighborhood */
  @Field()
  totalClubs: number;
}

@ObjectType()
export class PaginatedCities extends createPaginatedType(City) {}

export const CitySchema = SchemaFactory.createForClass(City);

// Create 2dsphere index on location.center for geospatial queries
CitySchema.index({ 'location.center': '2dsphere' });
