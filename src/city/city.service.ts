import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, PaginateModel } from 'mongoose';
import { PaginationInput, SortOrder } from 'src/common/pagination.input';
import { CreateCityInput } from './dto/create-city.input';
import { UpdateCityInput } from './dto/update-city.input';
import { City, NeighborhoodStats } from './entities/city.entity';
import { BarsService } from 'src/bars/bars.service';
import { ClubsService } from 'src/clubs/clubs.service';
import { NeighborhoodService } from 'src/neighborhood/neighborhood.service';

@Injectable()
export class CityService {
  constructor(
    @InjectModel(City.name)
    private cityModel: PaginateModel<City>,
    private barsService: BarsService,
    private clubsService: ClubsService,
    private neighborhoodService: NeighborhoodService,
  ) {}

  async create(createCityInput: CreateCityInput) {
    // Check if city with same name already exists
    const existingCity = await this.cityModel.findOne({
      name: { $regex: new RegExp(`^${createCityInput.name}$`, 'i') },
    });

    if (existingCity) {
      throw new ConflictException('City with this name already exists');
    }

    return this.cityModel.create(createCityInput);
  }

  async findAll(
    filter: FilterQuery<City> = {},
    paginationInput?: PaginationInput,
  ) {
    return this.cityModel.paginate(filter, paginationInput);
  }

  async findOne(id: string): Promise<City> {
    const city = await this.cityModel.findById(id);

    if (!city) {
      throw new NotFoundException(`City with ID ${id} not found`);
    }

    return city;
  }

  async update(id: string, updateCityInput: UpdateCityInput): Promise<City> {
    // Check if city exists
    const existingCity = await this.cityModel.findById(id);
    if (!existingCity) {
      throw new NotFoundException(`City with ID ${id} not found`);
    }

    // Check if name is being updated and if it conflicts with another city
    if (updateCityInput.name && updateCityInput.name !== existingCity.name) {
      const nameConflict = await this.cityModel.findOne({
        name: { $regex: new RegExp(`^${updateCityInput.name}$`, 'i') },
        _id: { $ne: id },
      });

      if (nameConflict) {
        throw new ConflictException('City with this name already exists');
      }
    }

    const updatedCity = await this.cityModel.findByIdAndUpdate(
      id,
      updateCityInput,
      { new: true },
    );

    return updatedCity!;
  }

  async remove(id: string): Promise<City> {
    const city = await this.cityModel.findByIdAndDelete(id);

    if (!city) {
      throw new NotFoundException(`City with ID ${id} not found`);
    }

    return city;
  }

  async getNeighborhoodStats(cityId: string): Promise<NeighborhoodStats[]> {
    // First, verify the city exists
    const city = await this.cityModel.findById(cityId);
    if (!city) {
      throw new NotFoundException(`City with ID ${cityId} not found`);
    }

    // Get all neighborhoods for this city using the neighborhood service
    const neighborhoodsResult = await this.neighborhoodService.findAll(
      { city: cityId },
      {
        limit: 1000,
        page: 1,
        sortConfig: [{ field: 'name', order: SortOrder.ASC }],
      }, // Get all neighborhoods
    );
    const neighborhoods = neighborhoodsResult.docs;

    // Build the result array with counts for each neighborhood
    const result: NeighborhoodStats[] = await Promise.all(
      neighborhoods.map(async (neighborhood) => {
        const totalBars = await this.barsService.count({
          city: cityId,
          neighborhood: neighborhood._id.toString(),
        });
        const totalClubs = await this.clubsService.count({
          city: cityId,
          neighborhood: neighborhood._id.toString(),
        });

        return {
          id: neighborhood._id.toString(),
          name: neighborhood.name,
          slug: neighborhood.slug,
          coverImage: neighborhood.coverImage,
          images: neighborhood.images,
          totalBars,
          totalClubs,
        };
      }),
    );

    // Also include bars/clubs that don't have a neighborhood assigned
    const barsWithoutNeighborhood = await this.barsService.count({
      city: cityId,
      neighborhood: { $exists: false },
    });
    const clubsWithoutNeighborhood = await this.clubsService.count({
      city: cityId,
      neighborhood: { $exists: false },
    });

    if (barsWithoutNeighborhood > 0 || clubsWithoutNeighborhood > 0) {
      result.push({
        id: 'no-neighborhood',
        name: 'Other Areas',
        slug: 'other-areas',
        coverImage: undefined,
        images: [],
        totalBars: barsWithoutNeighborhood,
        totalClubs: clubsWithoutNeighborhood,
      });
    }

    return result;
  }
}
