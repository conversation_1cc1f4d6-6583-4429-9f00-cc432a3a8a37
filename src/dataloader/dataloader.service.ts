/* eslint-disable @typescript-eslint/ban-ts-comment */
import { Injectable } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import * as DataLoader from 'dataloader';
import { Connection, Document } from 'mongoose';
import { City } from 'src/city/entities/city.entity';
import { ClubCategory } from 'src/clubs/entities/club-categories.entity';
import { BarCategory } from 'src/bars/entities/bar-categories.entity';
import { Neighborhood } from 'src/neighborhood/entity/neighborhood.entity';
import { User } from 'src/users/entities/user.entity';

@Injectable()
export class DataloaderService {
  constructor(@InjectConnection() private readonly connection: Connection) {}

  private createDataLoader<T extends Document>(modelName: string) {
    const model = this.connection.model<T>(modelName);

    // @ts-expect-error
    return new DataLoader<string, T>(async (keys) => {
      const results = await model.find({
        _id: { $in: [...new Set(keys.map(String))] },
      });

      return keys.map(
        (key) =>
          results.find((doc) => String(doc._id) === key.toString()) || null,
      );
    });
  }

  getLoaders() {
    const usersLoader = this.createDataLoader(User.name);
    const citiesLoader = this.createDataLoader(City.name);
    const clubCategoriesLoader = this.createDataLoader(ClubCategory.name);
    const barCategoriesLoader = this.createDataLoader(BarCategory.name);
    const neighborhoodsLoader = this.createDataLoader(Neighborhood.name);

    return {
      usersLoader,
      citiesLoader,
      clubCategoriesLoader,
      barCategoriesLoader,
      neighborhoodsLoader,
    };
  }
}
