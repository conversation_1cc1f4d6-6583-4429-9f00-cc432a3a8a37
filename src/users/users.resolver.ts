import { Args, Query, Resolver } from '@nestjs/graphql';
import { PaginatedUsers, User } from './entities/user.entity';
import { UsersService } from './users.service';
import { PaginationInput } from 'src/common/pagination.input';

@Resolver(() => User)
export class UsersResolver {
  constructor(private readonly usersService: UsersService) {}

  @Query(() => PaginatedUsers, { name: 'users' })
  findAll(
    @Args('paginationInput', { type: () => PaginationInput, nullable: true })
    paginationInput?: PaginationInput,
  ) {
    const filter = {};
    return this.usersService.findAll(filter, paginationInput);
  }

  @Query(() => User, { name: 'user' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.usersService.findOne({ _id: id });
  }
}
