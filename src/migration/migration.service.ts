import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { config, database, up } from 'migrate-mongo';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class MigrationService implements OnModuleInit {
  private readonly dbMigrationConfig: config.Config | null;

  constructor(private configService: ConfigService) {
    // Check if migrations directory exists
    const migrationsDir = path.join(__dirname, '../../migrations');
    if (!fs.existsSync(migrationsDir)) {
      try {
        fs.mkdirSync(migrationsDir, { recursive: true });
        console.log('Created migrations directory:', migrationsDir);
      } catch (error) {
        console.warn(
          'Warning: Cannot create migrations directory. Migrations will be skipped.',
          error instanceof Error ? error.message : String(error),
        );
        this.dbMigrationConfig = null;
        return;
      }
    }

    this.dbMigrationConfig = {
      mongodb: {
        databaseName: this.configService.get('DATABASE_NAME')!,
        url: this.configService.get('DATABASE_URI')!,
      },
      migrationsDir: migrationsDir,
      changelogCollectionName: 'migrations',
      migrationFileExtension: '.js',
    };
  }
  async onModuleInit() {
    if (!this.dbMigrationConfig) {
      console.log('Migrations skipped: No migration configuration available');
      return;
    }

    console.log('Checking for migrations');

    config.set(this.dbMigrationConfig);
    const { client, db } = await database.connect();
    await up(db, client);
  }
}
