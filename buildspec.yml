version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 22
    commands:
      - echo Installing dependencies...
      - npm install

  build:
    commands:
      - echo Building the application...
      - npm run build

  post_build:
    commands:
      - echo Build completed on `date`
      - echo "Copying package.json to dist..."
      - cp package.json dist/
      - echo "Listing files..."
      - find . -type f

artifacts:
  base-directory: dist
  files:
    - '**/*'
