# Database Migrations

This directory contains database migration files for the Seeker backend application.

## Overview

The migration system uses `migrate-mongo` to manage database schema changes and data migrations for MongoDB.

## Configuration

- **Migration Directory**: `migrations/`
- **Changelog Collection**: `migrations`
- **File Extension**: `.js`
- **Database**: Configured via environment variables (`DATABASE_URI`, `DATABASE_NAME`)

## How it Works

1. The `MigrationService` automatically runs on application startup
2. It checks for pending migrations in this directory
3. Applies any new migrations to the database
4. Tracks applied migrations in the `migrations` collection

## Creating Migrations

To create a new migration file, you can use the migrate-mongo CLI:

```bash
npx migrate-mongo create <migration-name>
```

This will create a new migration file in this directory with the following structure:

```javascript
module.exports = {
  async up(db, client) {
    // Migration code here
  },

  async down(db, client) {
    // Rollback code here
  }
};
```

## Directory Auto-Creation

The migrations directory is automatically created by the `MigrationService` if it doesn't exist, so you don't need to manually create it.
